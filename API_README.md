# JAST Partner Portal API

这是一个基于Laravel 12的纯API后端服务，为前端应用提供数据接口。

## 🚀 项目特性

- **纯API架构**: 移除了所有视图和前端资源，专注于API服务
- **标准化响应**: 统一的JSON响应格式
- **异常处理**: 完善的错误处理和响应机制
- **API版本控制**: 支持版本化的API端点
- **认证系统**: 基于Laravel Sanctum的API认证
- **CORS支持**: 跨域请求支持
- **健康检查**: 内置健康检查端点

## 📋 API端点

### 公共端点

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/api/health` | 健康检查 |
| GET | `/api/v1/status` | API状态信息 |
| GET | `/api/v1/health` | 详细健康检查 |

### 认证端点

| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| GET | `/api/v1/user` | 获取当前用户信息 | Bear<PERSON> |

## 📝 响应格式

### 成功响应
```json
{
    "success": true,
    "message": "Success",
    "data": {...},
    "timestamp": "2024-01-01T00:00:00.000000Z"
}
```

### 错误响应
```json
{
    "success": false,
    "message": "Error message",
    "errors": {...},
    "timestamp": "2024-01-01T00:00:00.000000Z"
}
```

### 验证错误响应
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "field_name": ["Error message"]
    },
    "timestamp": "2024-01-01T00:00:00.000000Z"
}
```

## 🔧 开发环境设置

### 使用Docker (推荐)

1. **启动项目**
   ```bash
   make up ENV=development
   ```

2. **安装依赖**
   ```bash
   make composer-install ENV=development
   ```

3. **运行迁移**
   ```bash
   make migrate ENV=development
   ```

### 可用的Make命令

- `make init [ENV=环境名]` - 初始化项目
- `make up [ENV=环境名]` - 启动容器
- `make down` - 停止容器
- `make composer cmd="命令"` - 运行Composer命令
- `make artisan cmd="命令"` - 运行Artisan命令
- `make migrate` - 运行数据库迁移
- `make test` - 运行测试

## 🏗️ 项目结构

```
app/
├── Http/
│   ├── Controllers/
│   │   └── Api/
│   │       └── V1/           # API v1 控制器
│   ├── Middleware/
│   │   └── ApiMiddleware.php # API中间件
│   ├── Requests/
│   │   └── ApiRequest.php    # API请求基类
│   └── Resources/
│       ├── ApiResource.php   # API资源基类
│       └── ApiCollection.php # API集合资源基类
├── Exceptions/
│   └── ApiException.php      # API异常类
└── ...
routes/
├── api.php                   # API路由
└── web.php                   # Web路由(已禁用)
```

## 🔐 认证

项目使用Laravel Sanctum进行API认证。

### 获取访问令牌
```bash
# 通过认证端点获取令牌
curl -X POST http://localhost/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'
```

响应示例：
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "user": {
      "id": 1,
      "name": "Test User",
      "email": "<EMAIL>",
      "email_verified_at": null
    },
    "token": "3|7JHCY3kjoLZPlvGUGjhDZLrRXMnuH88o0WAvUNjc33285326",
    "token_type": "Bearer"
  },
  "timestamp": "2025-06-05T11:10:47.593773Z"
}
```

### 使用令牌访问受保护的端点
```bash
# 获取当前用户信息
curl -X GET http://localhost/api/v1/user \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Accept: application/json"


```

### 退出登录
```bash
# 撤销当前令牌
curl -X POST http://localhost/api/v1/auth/logout \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Accept: application/json"

# 撤销所有令牌
curl -X POST http://localhost/api/v1/auth/revoke-all-tokens \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Accept: application/json"
```

## 🧪 测试

运行测试套件：
```bash
make test ENV=development
```

## 📊 健康检查

访问健康检查端点来验证服务状态：

```bash
# 简单健康检查
curl http://localhost/api/health

# 详细健康检查
curl http://localhost/api/v1/health
```

## 🔧 配置

主要配置文件：
- `.env.development` - 开发环境配置
- `.env.production` - 生产环境配置
- `bootstrap/app.php` - 应用引导配置

## 📈 性能优化

- 使用Redis作为缓存驱动
- 数据库连接池
- API响应缓存
- 查询优化

## 🚀 部署

生产环境部署：
```bash
make up ENV=production
make composer cmd="install --no-dev --optimize-autoloader" ENV=production
make artisan cmd="config:cache" ENV=production
make artisan cmd="route:cache" ENV=production
```
