<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Invitation;
use App\Models\Organisation;
use App\Models\User;
use App\Models\Role;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

final class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create sample organisations
        $activeOrg = Organisation::factory()->active()->create([
            'name' => 'Tech Solutions Ltd',
            'code' => 'TECH001',
            'details' => [
                'industry' => 'Technology',
                'size' => 'Medium',
                'founded' => 2020,
                'website' => 'https://techsolutions.example.com',
            ],
            'remarks' => 'Leading technology solutions provider',
        ]);

        $pendingOrg = Organisation::factory()->pending()->create([
            'name' => 'Healthcare Innovations',
            'code' => 'HEALTH001',
            'details' => [
                'industry' => 'Healthcare',
                'size' => 'Large',
                'founded' => 2018,
            ],
            'remarks' => 'Innovative healthcare solutions',
        ]);

        $suspendedOrg = Organisation::factory()->suspended()->create([
            'name' => 'Finance Corp',
            'code' => 'FIN001',
            'details' => [
                'industry' => 'Finance',
                'size' => 'Small',
                'founded' => 2019,
            ],
            'remarks' => 'Financial services company - currently under review',
        ]);

        // Preload permissions
        $this->call(PermissionSeeder::class);

        // Create test user with organisations
        $testUser = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'remember_token' => Str::random(10),
        ]);
        $testUser->organisations()->attach([$activeOrg->id, $pendingOrg->id]);

        // Assign owner role for activeOrg specifically
        $ownerRole = Role::where('name', 'owner')
            ->where('organisation_id', $activeOrg->id)
            ->first();

        if ($ownerRole) {
            // Set the team context for the organisation
            app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($activeOrg->id);
            $testUser->assignRole($ownerRole);
        }

        // Create additional users for different organisations
        $users1 = User::factory()->count(3)->create();
        foreach ($users1 as $user) {
            $user->organisations()->attach($activeOrg->id);
        }

        $users2 = User::factory()->count(2)->create();
        foreach ($users2 as $user) {
            $user->organisations()->attach($pendingOrg->id);
        }

        $user3 = User::factory()->create();
        $user3->organisations()->attach($suspendedOrg->id);

        // Create some users without organisation
        User::factory()->count(2)->create();

        // Create debug invitation record with 100 max uses for API testing
        Invitation::create([
            'model_type' => Organisation::class,
            'model_id' => $activeOrg->id,
            'role' => 'member',
            'created_by_user_id' => $testUser->id,
            'expires_at' => now()->addMonths(6), // Long expiration for debugging
            'max_uses' => 1000,
            'uses' => 0,
            'email_restriction' => null, // No email restriction for easy testing
        ]);
    }
}
