services:
  # PHP Application
  app:
    build:
      context: .
      dockerfile: docker/php/Dockerfile
      args:
        PHP_VERSION: ${DOCKER_PHP_VERSION:-8.4.7}
        USER_ID: ${HOST_UID:-1000}
        GROUP_ID: ${HOST_GID:-1000}
    container_name: jast-app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./logs:/var/www/html/storage/logs
    networks:
      - jast-network
    depends_on:
      - mysql
      - redis

  # Nginx Service
  nginx:
    image: nginx:${DOCKER_NGINX_VERSION:-1.25}-alpine
    container_name: jast-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./:/var/www
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
    networks:
      - jast-network
    depends_on:
      - app

  # MySQL Service
  mysql:
    image: mysql:${DOCKER_MYSQL_VERSION:-8.0}
    container_name: jast-mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ${MAIN_DB_DATABASE}
      #MYSQL_USER: ${MAIN_DB_USERNAME}
      #MYSQL_PASSWORD: ${MAIN_DB_PASSWORD}
      MYSQL_ROOT_PASSWORD: ${MAIN_DB_ROOT_PASSWORD}
      MYSQL_ROOT_HOST: ${MYSQL_ROOT_HOST:-localhost}
    command: >
      --bind-address=${MYSQL_BIND_ADDRESS:-127.0.0.1}
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - jast-network
    ports:
      - "3306:3306"

  # Redis Service
  redis:
    image: redis:${DOCKER_REDIS_VERSION:-7.0}-alpine
    container_name: jast-redis
    restart: unless-stopped
    volumes:
      - redis-data:/data
    networks:
      - jast-network
    ports:
      - "6379:6379"

networks:
  jast-network:
    driver: bridge

volumes:
  mysql-data:
    driver: local
  redis-data:
    driver: local
