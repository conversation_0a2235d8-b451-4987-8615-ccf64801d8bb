# Invitation Link Functionality Usage Guide

## Feature Overview

The invitation link functionality allows organization administrators to create invitation links. Users can join the corresponding organization with specified roles by clicking the invitation links.

## Database Structure

### Invitation Model Fields

- `id`: UUID type primary key
- `model_type`: Associated model type (e.g., 'App\Models\Organisation')
- `model_id`: Associated model instance ID
- `role`: Role permission name for the invitation (owner, member)
- `created_by_user_id`: User ID who issued the invitation
- `expires_at`: Expiration time, defaults to one week after creation
- `max_uses`: Maximum usage count, defaults to 1
- `uses`: Current usage count, defaults to 0
- `email_restriction`: Email restriction (optional)

## API Endpoints

### 1. Create Invitation (POST /api/v1/invitations)

**Permission Requirements**: Authentication required, user must be system administrator or organization administrator

**Request Parameters**:
```json
{
    "model_type": "App\\Models\\Organisation",
    "model_id": 1,
    "role": "member",
    "expires_at": "2025-07-01 00:00:00",
    "max_uses": 5,
    "email_restriction": "<EMAIL>"
}
```

**响应示例**:
```json
{
    "success": true,
    "message": "邀请创建成功",
    "data": {
        "id": "cmbix78tx0gk113phdxx52f1e",
        "model_type": "App\\Models\\Organisation",
        "model_id": 1,
        "role": "member",
        "expires_at": "2025-07-01T00:00:00.000000Z",
        "max_uses": 5,
        "uses": 0,
        "email_restriction": "<EMAIL>",
        "is_expired": false,
        "is_valid": true,
        "has_reached_usage_limit": false,
        "created_by": {
            "id": 1,
            "name": "Admin User",
            "email": "<EMAIL>"
        },
        "model_name": "Test Organization",
        "created_at": "2025-06-17T14:00:00.000000Z",
        "updated_at": "2025-06-17T14:00:00.000000Z"
    }
}
```

### 2. 查看邀请信息 (GET /api/v1/invitations/{id})

**权限要求**: 无需认证（但认证用户会获得额外的缓存优化）

**响应示例**:
```json
{
    "success": true,
    "message": "邀请信息获取成功",
    "data": {
        "id": "cmbix78tx0gk113phdxx52f1e",
        "model_type": "App\\Models\\Organisation",
        "model_id": 1,
        "role": "member",
        "expires_at": "2025-07-01T00:00:00.000000Z",
        "max_uses": 5,
        "uses": 0,
        "email_restriction": null,
        "is_expired": false,
        "is_valid": true,
        "has_reached_usage_limit": false,
        "created_by": {
            "id": 1,
            "name": "Admin User",
            "email": "<EMAIL>"
        },
        "model_name": "Test Organization",
        "created_at": "2025-06-17T14:00:00.000000Z",
        "updated_at": "2025-06-17T14:00:00.000000Z"
    }
}
```

### 3. 接受邀请 (POST /api/v1/invitations/{id}/accept)

**权限要求**: 需要认证

**响应示例**:
```json
{
    "success": true,
    "message": "邀请接受成功，已加入组织",
    "data": {
        "message": "成功加入组织",
        "organization": {
            "id": 1,
            "name": "Test Organization"
        },
        "role": "member",
        "user_roles": {
            "system_roles": [],
            "organisation_roles": [
                {
                    "id": 3,
                    "name": "member",
                    "organisation_id": 1
                }
            ],
            "all_role_names": ["member"]
        }
    }
}
```

## 使用流程

### 1. 管理员创建邀请

1. 组织管理员或系统管理员调用创建邀请API
2. 系统生成UUID作为邀请ID
3. 返回邀请信息，包含邀请链接

### 2. 用户访问邀请链接

1. 用户访问邀请页面（例如：`/join?id=xxx`）
2. 前端调用 `/api/v1/invitations/{id}` 获取邀请信息
3. 系统返回邀请详情（无需登录即可查看基本信息）
4. 如果用户已登录，系统会进行权限验证并缓存验证结果以优化后续接受操作

### 3. 用户接受邀请

1. 用户点击接受邀请按钮
2. 前端调用 `/api/v1/invitations/{id}/accept`
3. 系统验证邀请有效性
4. 将用户添加到组织并分配角色
5. 设置用户邮箱为已验证状态
6. 增加邀请使用次数
7. 返回成功信息和用户角色信息

## 安全特性

1. **系统角色保护**: 不允许通过邀请链接分配系统角色（root, admin）
2. **邮箱限制**: 可以限制特定邮箱才能使用邀请
3. **过期时间**: 邀请有过期时间限制
4. **使用次数限制**: 可以限制邀请的最大使用次数
5. **权限验证**: 只有有权限的用户才能创建邀请

## 错误处理

- **401**: 用户未登录（仅适用于接受邀请操作）
- **403**: 权限不足或邮箱不符合限制（仅适用于已登录用户查看邀请）
- **404**: 邀请不存在或角色不存在
- **410**: 邀请已过期或已达到使用上限
- **422**: 请求参数验证失败

## 测试

运行邀请功能测试：
```bash
make artisan cmd="test --filter=InvitationControllerTest" ENV=development
```

测试覆盖了以下场景：
- 未认证用户访问邀请（现在允许查看基本信息）
- 未认证用户无法获得缓存优化
- 有效邀请信息查看
- 过期邀请处理
- 使用次数限制
- 邮箱限制验证（仅对已登录用户）
- 系统角色保护（仅对已登录用户）
- 邀请接受流程（需要先查看邀请进行缓存）
