<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V1;

use App\Models\Invitation;
use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

final class InvitationControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private PermissionService $permissionService;
    private User $user;
    private User $adminUser;
    private User $ownerUser;
    private Organisation $organisation;
    private Organisation $otherOrganisation;
    private Role $memberRole;
    private Role $ownerRole;

    protected function setUp(): void
    {
        parent::setUp();

        $this->permissionService = app(PermissionService::class);

        // Create test organizations
        $this->organisation = Organisation::factory()->create([
            'name' => 'Test Organization',
            'status' => 'active',
        ]);

        $this->otherOrganisation = Organisation::factory()->create([
            'name' => 'Other Organization',
            'status' => 'active',
        ]);

        // Create roles for the organizations
        $this->memberRole = $this->permissionService->createRole('member', 'api', $this->organisation->id);
        $this->ownerRole = $this->permissionService->createRole('owner', 'api', $this->organisation->id);

        // Create test users
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'email_verified_at' => null,
        ]);

        $this->adminUser = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $this->ownerUser = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        // Assign admin role to admin user
        $adminRole = $this->permissionService->createRole('admin', 'system');
        $this->permissionService->assignRoleToUser($this->adminUser, $adminRole);

        // Assign owner role to owner user and add to organization
        $this->permissionService->assignRoleToUser($this->ownerUser, $this->ownerRole);
        $this->ownerUser->organisations()->attach($this->organisation->id);
    }

    // ========================================
    // Index Method Tests
    // ========================================

    public function test_index_without_authentication_returns_401(): void
    {
        $response = $this->getJson('/api/v1/invitations');

        $response->assertStatus(401);
    }

    public function test_index_as_system_admin_returns_all_invitations(): void
    {
        Sanctum::actingAs($this->adminUser);

        // Create invitations by different users
        $invitation1 = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'member',
            'created_by_user_id' => $this->adminUser->id,
        ]);

        $invitation2 = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'owner',
            'created_by_user_id' => $this->ownerUser->id,
        ]);

        $invitation3 = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->otherOrganisation->id,
            'role' => 'member',
            'created_by_user_id' => $this->user->id,
        ]);

        $response = $this->getJson('/api/v1/invitations');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Invitations retrieved successfully',
            ])
            ->assertJsonStructure([
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'model_type',
                            'model_id',
                            'role',
                            'expires_at',
                            'max_uses',
                            'uses',
                            'email_restriction',
                            'is_expired',
                            'is_valid',
                            'has_reached_usage_limit',
                            'created_by' => ['id', 'name', 'email'],
                            'model_name',
                            'created_at',
                            'updated_at',
                        ],
                    ],
                    'meta' => [
                        'current_page',
                        'last_page',
                        'per_page',
                        'total',
                        'from',
                        'to',
                    ],
                ],
            ]);

        // Verify all invitations are returned
        $responseData = $response->json('data.data');
        $this->assertCount(3, $responseData);

        $invitationIds = collect($responseData)->pluck('id')->toArray();
        $this->assertContains($invitation1->id, $invitationIds);
        $this->assertContains($invitation2->id, $invitationIds);
        $this->assertContains($invitation3->id, $invitationIds);
    }

    public function test_index_as_owner_returns_only_own_invitations(): void
    {
        Sanctum::actingAs($this->ownerUser);

        // Create invitations by different users
        $ownInvitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'member',
            'created_by_user_id' => $this->ownerUser->id,
        ]);

        $adminInvitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'owner',
            'created_by_user_id' => $this->adminUser->id,
        ]);

        $otherInvitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->otherOrganisation->id,
            'role' => 'member',
            'created_by_user_id' => $this->user->id,
        ]);

        $response = $this->getJson('/api/v1/invitations');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Invitations retrieved successfully',
            ]);

        // Verify only own invitations are returned
        $responseData = $response->json('data.data');
        $this->assertCount(1, $responseData);
        $this->assertEquals($ownInvitation->id, $responseData[0]['id']);
    }

    public function test_index_as_regular_user_returns_403(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/v1/invitations');

        $response->assertStatus(403)
            ->assertJson([
                'message' => 'This action is unauthorized.',
            ]);
    }

    public function test_index_with_filters_works_correctly(): void
    {
        Sanctum::actingAs($this->adminUser);

        // Create invitations with different attributes
        $memberInvitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'member',
            'created_by_user_id' => $this->adminUser->id,
        ]);

        $ownerInvitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'owner',
            'created_by_user_id' => $this->adminUser->id,
        ]);

        $otherOrgInvitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->otherOrganisation->id,
            'role' => 'member',
            'created_by_user_id' => $this->adminUser->id,
        ]);

        // Test role filter
        $response = $this->getJson('/api/v1/invitations?role=member');
        $response->assertStatus(200);
        $responseData = $response->json('data.data');
        $this->assertCount(2, $responseData);

        // Test model_id filter
        $response = $this->getJson("/api/v1/invitations?model_id={$this->organisation->id}");
        $response->assertStatus(200);
        $responseData = $response->json('data.data');
        $this->assertCount(2, $responseData);

        // Test model_type filter
        $response = $this->getJson('/api/v1/invitations?model_type=App\Models\Organisation');
        $response->assertStatus(200);
        $responseData = $response->json('data.data');
        $this->assertCount(3, $responseData);
    }

    public function test_index_with_status_filter_works_correctly(): void
    {
        Sanctum::actingAs($this->adminUser);

        // Create valid invitation
        $validInvitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'member',
            'created_by_user_id' => $this->adminUser->id,
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 5,
            'uses' => 0,
        ]);

        // Create expired invitation
        $expiredInvitation = Invitation::factory()->expired()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'member',
            'created_by_user_id' => $this->adminUser->id,
        ]);

        // Create used up invitation
        $usedUpInvitation = Invitation::factory()->usageLimitReached()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'member',
            'created_by_user_id' => $this->adminUser->id,
        ]);

        // Test valid status filter
        $response = $this->getJson('/api/v1/invitations?status=valid');
        $response->assertStatus(200);
        $responseData = $response->json('data.data');
        $this->assertCount(1, $responseData);
        $this->assertEquals($validInvitation->id, $responseData[0]['id']);

        // Test expired status filter
        $response = $this->getJson('/api/v1/invitations?status=expired');
        $response->assertStatus(200);
        $responseData = $response->json('data.data');
        $this->assertCount(1, $responseData);
        $this->assertEquals($expiredInvitation->id, $responseData[0]['id']);

        // Test used_up status filter
        $response = $this->getJson('/api/v1/invitations?status=used_up');
        $response->assertStatus(200);
        $responseData = $response->json('data.data');
        $this->assertCount(1, $responseData);
        $this->assertEquals($usedUpInvitation->id, $responseData[0]['id']);
    }

    public function test_index_pagination_works_correctly(): void
    {
        Sanctum::actingAs($this->adminUser);

        // Create multiple invitations
        $invitations = Invitation::factory()->count(25)->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'member',
            'created_by_user_id' => $this->adminUser->id,
        ]);

        // Test first page
        $response = $this->getJson('/api/v1/invitations?per_page=10');
        $response->assertStatus(200);

        $meta = $response->json('data.meta');
        $this->assertEquals(1, $meta['current_page']);
        $this->assertEquals(3, $meta['last_page']); // 25 items / 10 per page = 3 pages
        $this->assertEquals(10, $meta['per_page']);
        $this->assertEquals(25, $meta['total']);
        $this->assertEquals(1, $meta['from']);
        $this->assertEquals(10, $meta['to']);

        $responseData = $response->json('data.data');
        $this->assertCount(10, $responseData);

        // Test second page
        $response = $this->getJson('/api/v1/invitations?per_page=10&page=2');
        $response->assertStatus(200);

        $meta = $response->json('data.meta');
        $this->assertEquals(2, $meta['current_page']);
        $this->assertEquals(11, $meta['from']);
        $this->assertEquals(20, $meta['to']);

        $responseData = $response->json('data.data');
        $this->assertCount(10, $responseData);
    }

    // ========================================
    // Show Method Tests
    // ========================================

    public function test_show_invitation_without_authentication_returns_invitation_info(): void
    {
        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'member',
            'created_by_user_id' => $this->adminUser->id,
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 5,
            'uses' => 2,
        ]);

        $response = $this->getJson("/api/v1/invitations/{$invitation->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Invitation information retrieved successfully',
            ])
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'model_type',
                    'model_id',
                    'role',
                    'expires_at',
                    'max_uses',
                    'uses',
                    'email_restriction',
                    'is_expired',
                    'is_valid',
                    'has_reached_usage_limit',
                    'created_by' => ['id', 'name', 'email'],
                    'model_name',
                    'created_at',
                    'updated_at',
                ],
            ]);
    }



    public function test_show_valid_invitation_returns_invitation_info(): void
    {
        Sanctum::actingAs($this->user);

        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'member',
            'created_by_user_id' => $this->adminUser->id,
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 5,
            'uses' => 2,
            'email_restriction' => null, // No email restriction
        ]);

        $response = $this->getJson("/api/v1/invitations/{$invitation->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Invitation information retrieved successfully',
            ])
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'model_type',
                    'model_id',
                    'role',
                    'expires_at',
                    'max_uses',
                    'uses',
                    'email_restriction',
                    'is_expired',
                    'is_valid',
                    'has_reached_usage_limit',
                    'created_by' => ['id', 'name', 'email'],
                    'model_name',
                    'created_at',
                    'updated_at',
                ],
            ]);
    }

    public function test_show_expired_invitation_returns_410(): void
    {
        Sanctum::actingAs($this->user);

        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'member',
            'created_by_user_id' => $this->adminUser->id,
            'expires_at' => Carbon::now()->subDay(), // Expired
            'max_uses' => 5,
            'uses' => 0,
        ]);

        $response = $this->getJson("/api/v1/invitations/{$invitation->id}");

        $response->assertStatus(410)
            ->assertJson([
                'success' => false,
                'message' => 'Invitation link has expired',
            ]);
    }

    public function test_show_invitation_with_reached_usage_limit_returns_410(): void
    {
        Sanctum::actingAs($this->user);

        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'member',
            'created_by_user_id' => $this->adminUser->id,
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 1,
            'uses' => 1, // Reached limit
        ]);

        $response = $this->getJson("/api/v1/invitations/{$invitation->id}");

        $response->assertStatus(410)
            ->assertJson([
                'success' => false,
                'message' => 'Invitation link has reached usage limit',
            ]);
    }

    public function test_show_invitation_with_email_restriction_allows_viewing(): void
    {
        Sanctum::actingAs($this->user);

        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'member',
            'created_by_user_id' => $this->adminUser->id,
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 5,
            'uses' => 0,
            'email_restriction' => '<EMAIL>', // Different from user's email
        ]);

        $response = $this->getJson("/api/v1/invitations/{$invitation->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Invitation information retrieved successfully',
            ]);
    }

    public function test_show_system_role_invitation_returns_403(): void
    {
        Sanctum::actingAs($this->user);

        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'admin', // System role
            'created_by_user_id' => $this->adminUser->id,
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 5,
            'uses' => 0,
            'email_restriction' => null, // No email restriction
        ]);

        $response = $this->getJson("/api/v1/invitations/{$invitation->id}/accept");

        $response->assertStatus(403)
            ->assertJson([
                'message' => 'This action is unauthorized.',
            ]);
    }

    public function test_accept_invitation_without_authentication_returns_401(): void
    {
        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'member',
            'created_by_user_id' => $this->adminUser->id,
            'expires_at' => Carbon::now()->addWeek(),
        ]);

        $response = $this->postJson("/api/v1/invitations/{$invitation->id}/accept");

        $response->assertStatus(401);
    }

    public function test_accept_valid_invitation_assigns_role_and_adds_to_organization(): void
    {
        Sanctum::actingAs($this->user);

        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'member',
            'created_by_user_id' => $this->adminUser->id,
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 5,
            'uses' => 0,
            'email_restriction' => null, // No email restriction
        ]);



        $response = $this->postJson("/api/v1/invitations/{$invitation->id}/accept");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Invitation accepted successfully, joined organization',
            ])
            ->assertJsonStructure([
                'data' => [
                    'message',
                    'organization' => ['id', 'name'],
                    'role',
                    'user_roles',
                ],
            ]);

        // Verify user is added to organization
        $this->assertTrue($this->user->organisations()->where('organisation_id', $this->organisation->id)->exists());

        // Verify user has the role
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->assertTrue($this->user->hasRole('member'));

        // Verify email is verified
        $this->user->refresh();
        $this->assertNotNull($this->user->email_verified_at);

        // Verify invitation usage is incremented
        $invitation->refresh();
        $this->assertEquals(1, $invitation->uses);
    }

    public function test_accept_invitation_with_email_restriction_denies_wrong_email(): void
    {
        Sanctum::actingAs($this->user);

        $invitation = Invitation::factory()->create([
            'model_type' => Organisation::class,
            'model_id' => $this->organisation->id,
            'role' => 'member',
            'created_by_user_id' => $this->adminUser->id,
            'expires_at' => Carbon::now()->addWeek(),
            'max_uses' => 5,
            'uses' => 0,
            'email_restriction' => '<EMAIL>', // Different from user's email
        ]);

        $response = $this->postJson("/api/v1/invitations/{$invitation->id}/accept");

        $response->assertStatus(403)
            ->assertJson([
                'success' => false,
                'message' => 'Your email address is not allowed to accept this invitation',
            ]);
    }










}
