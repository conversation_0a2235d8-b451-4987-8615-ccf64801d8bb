<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V1;

use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

final class OrganisationControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private string $token;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        // Give the user system admin role for organisation management
        $adminRole = Role::firstOrCreate([
            'name' => 'admin',
            'guard_name' => 'system', // System-wide role requires 'system' guard
            'organisation_id' => null, // System-wide role
        ]);

        // Set the user's guard to system and assign the role
        $this->user->guard_name = 'system';
        $this->user->assignRole($adminRole);

        $this->token = $this->user->createToken('test-token')->plainTextToken;
    }

    public function test_can_get_organisations_list(): void
    {
        Organisation::factory()->count(5)->create();

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson('/api/v1/organisations');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'code',
                            'details',
                            'remarks',
                            'status',
                            'is_active',
                            'is_pending',
                            'is_suspended',
                            'created_at',
                            'updated_at',
                        ]
                    ],
                    'meta' => [
                        'total',
                        'per_page',
                        'current_page',
                        'last_page',
                        'from',
                        'to',
                    ]
                ],
                'timestamp'
            ])
            ->assertJson([
                'success' => true,
                'message' => '获取组织列表成功',
            ]);
    }

    public function test_can_filter_organisations_by_status(): void
    {
        Organisation::factory()->active()->count(3)->create();
        Organisation::factory()->pending()->count(2)->create();

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson('/api/v1/organisations?status=active');

        $response->assertStatus(200)
            ->assertJsonPath('data.meta.total', 3);
    }

    public function test_can_create_organisation(): void
    {
        $data = [
            'name' => 'Test Organisation',
            'code' => 'TEST001',
            'details' => ['industry' => 'Technology'],
            'remarks' => 'Test remarks',
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson('/api/v1/organisations', $data);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'code',
                    'details',
                    'remarks',
                    'status',
                    'is_active',
                    'is_pending',
                    'is_suspended',
                    'created_at',
                    'updated_at',
                ],
                'timestamp'
            ])
            ->assertJson([
                'success' => true,
                'message' => '组织创建成功',
                'data' => [
                    'name' => 'Test Organisation',
                    'code' => 'TEST001',
                    'details' => ['industry' => 'Technology'],
                    'remarks' => 'Test remarks',
                    'status' => 'pending', // Default status
                    'is_pending' => true,
                ]
            ]);

        $this->assertDatabaseHas('organisations', [
            'name' => 'Test Organisation',
            'code' => 'TEST001',
            'status' => 'pending',
        ]);
    }

    public function test_create_organisation_validation_fails_for_missing_required_fields(): void
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson('/api/v1/organisations', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name', 'code']);
    }

    public function test_create_organisation_validation_fails_for_duplicate_code(): void
    {
        Organisation::factory()->create(['code' => 'DUPLICATE']);

        $data = [
            'name' => 'Test Organisation',
            'code' => 'DUPLICATE',
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson('/api/v1/organisations', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['code']);
    }

    public function test_can_show_organisation(): void
    {
        $organisation = Organisation::factory()->create([
            'name' => 'Test Organisation',
            'code' => 'TEST001',
        ]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson("/api/v1/organisations/{$organisation->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '获取组织详情成功',
                'data' => [
                    'id' => $organisation->id,
                    'name' => 'Test Organisation',
                    'code' => 'TEST001',
                ]
            ]);
    }

    public function test_show_organisation_returns_404_for_non_existent(): void
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson('/api/v1/organisations/999');

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'Resource not found',
            ]);
    }

    public function test_can_update_organisation(): void
    {
        $organisation = Organisation::factory()->create([
            'name' => 'Old Name',
            'code' => 'OLD001',
        ]);

        $updateData = [
            'name' => 'New Name',
            'code' => 'NEW001',
            'remarks' => 'Updated remarks',
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->putJson("/api/v1/organisations/{$organisation->id}", $updateData);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '组织更新成功',
                'data' => [
                    'id' => $organisation->id,
                    'name' => 'New Name',
                    'code' => 'NEW001',
                    'remarks' => 'Updated remarks',
                ]
            ]);

        $this->assertDatabaseHas('organisations', [
            'id' => $organisation->id,
            'name' => 'New Name',
            'code' => 'NEW001',
            'remarks' => 'Updated remarks',
        ]);
    }

    public function test_update_organisation_returns_404_for_non_existent(): void
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->putJson('/api/v1/organisations/999', ['name' => 'New Name']);

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'Resource not found',
            ]);
    }



    public function test_can_suspend_organisation(): void
    {
        $organisation = Organisation::factory()->active()->create();

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson("/api/v1/organisations/{$organisation->id}/suspend");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '组织暂停成功',
                'data' => [
                    'id' => $organisation->id,
                    'status' => 'suspended',
                    'is_suspended' => true,
                ]
            ]);

        $this->assertDatabaseHas('organisations', [
            'id' => $organisation->id,
            'status' => 'suspended',
        ]);
    }

    public function test_suspend_already_suspended_organisation_returns_error(): void
    {
        $organisation = Organisation::factory()->suspended()->create();

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson("/api/v1/organisations/{$organisation->id}/suspend");

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => '组织已经是暂停状态',
            ]);
    }

    public function test_organisation_endpoints_require_authentication(): void
    {
        $organisation = Organisation::factory()->create();

        // Test index endpoint
        $response = $this->getJson('/api/v1/organisations');
        $response->assertStatus(401);

        // Test store endpoint
        $response = $this->postJson('/api/v1/organisations', []);
        $response->assertStatus(401);

        // Test show endpoint
        $response = $this->getJson("/api/v1/organisations/{$organisation->id}");
        $response->assertStatus(401);

        // Test update endpoint
        $response = $this->putJson("/api/v1/organisations/{$organisation->id}", []);
        $response->assertStatus(401);

        // Test suspend endpoint
        $response = $this->postJson("/api/v1/organisations/{$organisation->id}/suspend");
        $response->assertStatus(401);
    }

    public function test_organisation_owner_can_view_own_organisation(): void
    {
        // Create organisation owner user instead of system admin
        $owner = User::factory()->create([
            'name' => 'Organisation Owner',
            'email' => '<EMAIL>',
        ]);

        $org = Organisation::factory()->create([
            'name' => 'Owner Test Organisation',
            'code' => 'OWNER001',
        ]);

        // Associate user with organisation
        $owner->organisations()->attach($org->id);

        // Assign owner role
        $ownerRole = Role::firstOrCreate([
            'name' => 'owner',
            'guard_name' => 'api',
            'organisation_id' => $org->id,
        ]);

        // Set team context and assign role
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($org->id);
        $owner->assignRole($ownerRole);

        $token = $owner->createToken('test')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson("/api/v1/organisations/{$org->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '获取组织详情成功',
                'data' => [
                    'id' => $org->id,
                    'name' => 'Owner Test Organisation',
                    'code' => 'OWNER001',
                ]
            ]);
    }

    public function test_organisation_owner_cannot_view_other_organisation(): void
    {
        // Create organisation owner user
        $owner = User::factory()->create([
            'name' => 'Organisation Owner',
            'email' => '<EMAIL>',
        ]);

        $ownOrg = Organisation::factory()->create([
            'name' => 'Own Organisation',
            'code' => 'OWN001',
        ]);

        $otherOrg = Organisation::factory()->create([
            'name' => 'Other Organisation',
            'code' => 'OTHER001',
        ]);

        // Associate user with their own organisation
        $owner->organisations()->attach($ownOrg->id);

        // Assign owner role
        $ownerRole = Role::firstOrCreate([
            'name' => 'owner',
            'guard_name' => 'api',
            'organisation_id' => $ownOrg->id,
        ]);

        // Set team context and assign role
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($ownOrg->id);
        $owner->assignRole($ownerRole);

        $token = $owner->createToken('test')->plainTextToken;

        // Attempting to access other organisation should return 403
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson("/api/v1/organisations/{$otherOrg->id}");

        $response->assertStatus(403);
    }

    public function test_organisation_member_cannot_view_organisation(): void
    {
        // Create organisation member user
        $member = User::factory()->create([
            'name' => 'Organisation Member',
            'email' => '<EMAIL>',
        ]);

        $org = Organisation::factory()->create([
            'name' => 'Member Test Organisation',
            'code' => 'MEMBER001',
        ]);

        // Associate user with organisation
        $member->organisations()->attach($org->id);

        // Assign member role
        $memberRole = Role::firstOrCreate([
            'name' => 'member',
            'guard_name' => 'api',
            'organisation_id' => $org->id,
        ]);

        // Set team context and assign role
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($org->id);
        $member->assignRole($memberRole);

        $token = $member->createToken('test')->plainTextToken;

        // Member user should not be able to view organisation details
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson("/api/v1/organisations/{$org->id}");

        $response->assertStatus(403);
    }
}
