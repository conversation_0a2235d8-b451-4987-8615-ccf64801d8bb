<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\Organisation;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/**
 * Test DatabaseSeeder functionality
 */
final class DatabaseSeederTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that DatabaseSeeder creates test user with correct role assignment
     */
    public function test_database_seeder_creates_test_user_with_correct_roles(): void
    {
        // Run the seeder
        $this->seed();

        // Find the test user
        $testUser = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($testUser, 'Test user should be created');
        $this->assertEquals('Test User', $testUser->name);

        // Check user is associated with organizations
        $organisations = $testUser->organisations;
        $this->assertCount(2, $organisations, 'Test user should belong to 2 organisations');

        // Find the active organisation (Tech Solutions Ltd)
        $activeOrg = Organisation::where('name', 'Tech Solutions Ltd')->first();
        $this->assertNotNull($activeOrg, 'Active organisation should exist');
        $this->assertEquals('active', $activeOrg->status);

        // Check if user belongs to the active organisation
        $this->assertTrue(
            $testUser->belongsToOrganisation($activeOrg->id),
            'Test user should belong to active organisation'
        );

        // Set team context for the active organisation
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($activeOrg->id);

        // Check if user has owner role in the active organisation
        $hasOwnerRole = $testUser->hasRole('owner');
        $this->assertTrue($hasOwnerRole, 'Test user should have owner role in active organisation');

        // Verify the role is specifically for this organisation
        $ownerRole = $testUser->roles()
            ->where('roles.name', 'owner')
            ->where('roles.organisation_id', $activeOrg->id)
            ->first();
        
        $this->assertNotNull($ownerRole, 'Owner role should be assigned for the specific organisation');
        $this->assertEquals($activeOrg->id, $ownerRole->organisation_id);
        $this->assertEquals('api', $ownerRole->guard_name);
    }

    /**
     * Test that test user doesn't have owner role in other organisations
     */
    public function test_test_user_does_not_have_owner_role_in_other_organisations(): void
    {
        // Run the seeder
        $this->seed();

        $testUser = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($testUser);

        // Find the pending organisation (Healthcare Innovations)
        $pendingOrg = Organisation::where('name', 'Healthcare Innovations')->first();
        $this->assertNotNull($pendingOrg);

        // Set team context for the pending organisation
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($pendingOrg->id);

        // Check if user does NOT have owner role in the pending organisation
        $hasOwnerRole = $testUser->hasRole('owner');
        $this->assertFalse($hasOwnerRole, 'Test user should NOT have owner role in pending organisation');
    }

    /**
     * Test that only one owner role exists per organisation
     */
    public function test_only_one_owner_role_per_organisation(): void
    {
        // Run the seeder
        $this->seed();

        $activeOrg = Organisation::where('name', 'Tech Solutions Ltd')->first();
        $this->assertNotNull($activeOrg);

        // Count users with owner role in this organisation
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($activeOrg->id);

        $ownersCount = User::whereHas('roles', function ($query) use ($activeOrg) {
            $query->where('roles.name', 'owner')
                ->where('roles.organisation_id', $activeOrg->id);
        })->count();

        $this->assertEquals(1, $ownersCount, 'There should be exactly one owner per organisation');
    }
}
