<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionService;
use App\Services\UserService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class UserServiceTest extends TestCase
{
    use RefreshDatabase;

    private UserService $userService;
    private PermissionService $permissionService;
    private Organisation $organisation;
    private User $ownerUser;
    private User $memberUser;
    private Role $ownerRole;
    private Role $memberRole;

    protected function setUp(): void
    {
        parent::setUp();

        $this->userService = app(UserService::class);
        $this->permissionService = app(PermissionService::class);

        // Create test organisation
        $this->organisation = Organisation::factory()->create([
            'name' => 'Test Organisation',
            'status' => 'active',
        ]);

        // Create test users
        $this->ownerUser = User::factory()->create(['name' => 'Owner User']);
        $this->memberUser = User::factory()->create(['name' => 'Member User']);

        // Attach users to organisation
        $this->ownerUser->organisations()->attach($this->organisation->id);
        $this->memberUser->organisations()->attach($this->organisation->id);

        // Create roles
        $this->ownerRole = $this->permissionService->createRole('owner', 'api', $this->organisation->id);
        $this->memberRole = $this->permissionService->createRole('member', 'api', $this->organisation->id);

        // Assign roles
        $this->permissionService->assignRoleToUser($this->ownerUser, $this->ownerRole);
        $this->permissionService->assignRoleToUser($this->memberUser, $this->memberRole);
    }

    public function test_remove_from_organisation_prevents_removing_owner(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Cannot remove organisation owner. Owner role must be transferred first.');

        $this->userService->removeFromOrganisation($this->ownerUser, $this->organisation->id);
    }

    public function test_remove_from_organisation_allows_removing_non_owner(): void
    {
        $result = $this->userService->removeFromOrganisation($this->memberUser, $this->organisation->id);

        $this->assertInstanceOf(User::class, $result);
        $this->assertFalse($this->memberUser->organisations()->where('organisations.id', $this->organisation->id)->exists());
    }

    public function test_sync_organisations_prevents_removing_owner_from_organisation(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage("Cannot remove user from organisation ID {$this->organisation->id}. User is the owner and owner role must be transferred first.");

        // Try to sync to empty array (removing from all organisations)
        $this->userService->syncOrganisations($this->ownerUser, []);
    }

    public function test_sync_organisations_allows_removing_non_owner_from_organisation(): void
    {
        $result = $this->userService->syncOrganisations($this->memberUser, []);

        $this->assertInstanceOf(User::class, $result);
        $this->assertFalse($this->memberUser->organisations()->where('organisations.id', $this->organisation->id)->exists());
    }

    public function test_sync_organisations_allows_owner_to_stay_in_organisation(): void
    {
        $result = $this->userService->syncOrganisations($this->ownerUser, [$this->organisation->id]);

        $this->assertInstanceOf(User::class, $result);
        $this->assertTrue($this->ownerUser->organisations()->where('organisations.id', $this->organisation->id)->exists());
    }

    public function test_sync_organisations_allows_adding_organisations(): void
    {
        $newOrganisation = Organisation::factory()->create(['name' => 'New Organisation']);

        $result = $this->userService->syncOrganisations($this->memberUser, [$this->organisation->id, $newOrganisation->id]);

        $this->assertInstanceOf(User::class, $result);
        $this->assertTrue($this->memberUser->organisations()->where('organisations.id', $this->organisation->id)->exists());
        $this->assertTrue($this->memberUser->organisations()->where('organisations.id', $newOrganisation->id)->exists());
    }
}
