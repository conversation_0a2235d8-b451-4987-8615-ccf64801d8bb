<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Models\Organisation;
use App\Services\OrganisationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class OrganisationServiceTest extends TestCase
{
    use RefreshDatabase;

    private OrganisationService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new OrganisationService();
    }

    public function test_get_all_returns_paginated_organisations(): void
    {
        Organisation::factory()->count(20)->create();

        $result = $this->service->getAll(10);

        $this->assertEquals(10, $result->perPage());
        $this->assertEquals(20, $result->total());
        $this->assertEquals(2, $result->lastPage());
    }

    public function test_get_by_id_returns_organisation(): void
    {
        $organisation = Organisation::factory()->create();

        $result = $this->service->getById($organisation->id);

        $this->assertNotNull($result);
        $this->assertEquals($organisation->id, $result->id);
    }

    public function test_get_by_id_returns_null_for_non_existent(): void
    {
        $result = $this->service->getById(999);

        $this->assertNull($result);
    }

    public function test_get_by_code_returns_organisation(): void
    {
        $organisation = Organisation::factory()->create(['code' => 'TEST123']);

        $result = $this->service->getByCode('TEST123');

        $this->assertNotNull($result);
        $this->assertEquals('TEST123', $result->code);
    }

    public function test_get_by_code_returns_null_for_non_existent(): void
    {
        $result = $this->service->getByCode('NONEXISTENT');

        $this->assertNull($result);
    }

    public function test_create_organisation(): void
    {
        $data = [
            'name' => 'Test Organisation',
            'code' => 'TEST001',
            'details' => ['industry' => 'Technology'],
            'remarks' => 'Test remarks',
            'status' => 'active',
        ];

        $organisation = $this->service->create($data);

        $this->assertInstanceOf(Organisation::class, $organisation);
        $this->assertEquals($data['name'], $organisation->name);
        $this->assertEquals($data['code'], $organisation->code);
        $this->assertEquals($data['details'], $organisation->details);
        $this->assertEquals($data['remarks'], $organisation->remarks);
        $this->assertEquals($data['status'], $organisation->status);
    }

    public function test_update_organisation(): void
    {
        $organisation = Organisation::factory()->create(['name' => 'Old Name']);
        $updateData = ['name' => 'New Name'];

        $result = $this->service->update($organisation, $updateData);

        $this->assertTrue($result);
        $this->assertEquals('New Name', $organisation->fresh()->name);
    }



    public function test_suspend_organisation(): void
    {
        $organisation = Organisation::factory()->active()->create();

        $result = $this->service->suspend($organisation);

        $this->assertTrue($result);
        $this->assertTrue($organisation->fresh()->isSuspended());
    }

    public function test_get_by_status_returns_filtered_organisations(): void
    {
        Organisation::factory()->active()->count(5)->create();
        Organisation::factory()->pending()->count(3)->create();
        Organisation::factory()->suspended()->count(2)->create();

        $activeResult = $this->service->getByStatus('active');
        $pendingResult = $this->service->getByStatus('pending');
        $suspendedResult = $this->service->getByStatus('suspended');

        $this->assertEquals(5, $activeResult->total());
        $this->assertEquals(3, $pendingResult->total());
        $this->assertEquals(2, $suspendedResult->total());
    }

    public function test_is_code_unique_returns_true_for_unique_code(): void
    {
        $result = $this->service->isCodeUnique('UNIQUE123');

        $this->assertTrue($result);
    }

    public function test_is_code_unique_returns_false_for_existing_code(): void
    {
        Organisation::factory()->create(['code' => 'EXISTING123']);

        $result = $this->service->isCodeUnique('EXISTING123');

        $this->assertFalse($result);
    }

    public function test_is_code_unique_excludes_specified_id(): void
    {
        $organisation = Organisation::factory()->create(['code' => 'TEST123']);

        $result = $this->service->isCodeUnique('TEST123', $organisation->id);

        $this->assertTrue($result);
    }

    public function test_get_active_organisations(): void
    {
        Organisation::factory()->active()->count(3)->create();
        Organisation::factory()->pending()->count(2)->create();

        $result = $this->service->getActive();

        $this->assertEquals(3, $result->total());
    }

    public function test_get_pending_organisations(): void
    {
        Organisation::factory()->active()->count(3)->create();
        Organisation::factory()->pending()->count(2)->create();

        $result = $this->service->getPending();

        $this->assertEquals(2, $result->total());
    }

    public function test_get_suspended_organisations(): void
    {
        Organisation::factory()->active()->count(3)->create();
        Organisation::factory()->suspended()->count(1)->create();

        $result = $this->service->getSuspended();

        $this->assertEquals(1, $result->total());
    }
}
