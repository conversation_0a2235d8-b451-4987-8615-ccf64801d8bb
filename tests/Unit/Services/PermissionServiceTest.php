<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

final class PermissionServiceTest extends TestCase
{
    use RefreshDatabase;

    private PermissionService $permissionService;
    private Organisation $organisation;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->permissionService = app(PermissionService::class);

        // Create test organisation
        $this->organisation = Organisation::factory()->create([
            'name' => 'Test Organisation',
            'status' => 'active',
        ]);

        // Create test user
        $this->user = User::factory()->create();
        $this->user->organisations()->attach($this->organisation->id);
    }

    public function test_can_create_role_for_organisation(): void
    {
        $role = $this->permissionService->createRole('test-role', 'api', $this->organisation->id);

        $this->assertInstanceOf(Role::class, $role);
        $this->assertEquals('test-role', $role->name);
        $this->assertEquals('api', $role->guard_name);
        $this->assertEquals($this->organisation->id, $role->organisation_id);
    }

    public function test_can_assign_role_to_user(): void
    {
        $role = $this->permissionService->createRole('test-role', 'api', $this->organisation->id);

        $this->permissionService->assignRoleToUser($this->user, $role);

        $this->assertTrue($this->user->hasRole($role, 'api', $this->organisation->id));
    }

    public function test_can_remove_role_from_user(): void
    {
        $role = $this->permissionService->createRole('test-role', 'api', $this->organisation->id);

        $this->permissionService->assignRoleToUser($this->user, $role);
        $this->assertTrue($this->user->hasRole($role, 'api', $this->organisation->id));

        $this->permissionService->removeRoleFromUser($this->user, $role);
        $this->assertFalse($this->user->hasRole($role, 'api', $this->organisation->id));
    }

    public function test_can_sync_user_roles(): void
    {
        $role1 = $this->permissionService->createRole('role-1', 'api', $this->organisation->id);
        $role2 = $this->permissionService->createRole('role-2', 'api', $this->organisation->id);

        $this->permissionService->syncUserRoles($this->user, ['role-1', 'role-2'], $this->organisation->id);

        $this->assertTrue($this->user->hasRole('role-1', 'api', $this->organisation->id));
        $this->assertTrue($this->user->hasRole('role-2', 'api', $this->organisation->id));
    }

    public function test_can_check_user_has_role(): void
    {
        $role = $this->permissionService->createRole('admin', 'api', $this->organisation->id);
        $this->permissionService->assignRoleToUser($this->user, $role);

        // Set team context and check role directly
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->assertTrue($this->user->hasRole('admin'));
    }

    public function test_can_get_user_roles(): void
    {
        $role1 = $this->permissionService->createRole('admin', 'api', $this->organisation->id);
        $role2 = $this->permissionService->createRole('manager', 'api', $this->organisation->id);

        $this->permissionService->assignRoleToUser($this->user, $role1);
        $this->permissionService->assignRoleToUser($this->user, $role2);

        $userRoles = $this->permissionService->getUserRoles($this->user);

        $this->assertCount(2, $userRoles);
        $this->assertTrue($userRoles->contains('admin'));
        $this->assertTrue($userRoles->contains('manager'));
    }

    public function test_can_get_organisation_roles(): void
    {
        $this->permissionService->createRole('admin', 'api', $this->organisation->id);
        $this->permissionService->createRole('manager', 'api', $this->organisation->id);

        $roles = $this->permissionService->getOrganisationRoles($this->organisation->id);

        $this->assertCount(2, $roles);
    }

    public function test_creates_default_roles_for_organisation(): void
    {
        $roles = $this->permissionService->createDefaultRoles($this->organisation->id);

        $this->assertCount(2, $roles);

        $roleNames = collect($roles)->pluck('name')->toArray();
        $this->assertContains('owner', $roleNames);
        $this->assertContains('member', $roleNames);

        // Check all roles have correct guard_name and organisation_id
        foreach ($roles as $role) {
            $this->assertEquals('api', $role->guard_name);
            $this->assertEquals($this->organisation->id, $role->organisation_id);
        }
    }

    public function test_can_get_user_complete_role_info(): void
    {
        // Create and assign roles to user
        $memberRole = $this->permissionService->createRole('member', 'api', $this->organisation->id);
        $this->permissionService->assignRoleToUser($this->user, $memberRole);

        // Test the method
        $roleInfo = $this->permissionService->getUserCompleteRoleInfo($this->user);

        $this->assertIsArray($roleInfo);
        $this->assertArrayHasKey('system_roles', $roleInfo);
        $this->assertArrayHasKey('organisation_roles', $roleInfo);
        $this->assertArrayHasKey('all_role_names', $roleInfo);

        // Should have one organisation role
        $this->assertCount(1, $roleInfo['organisation_roles']);
        $this->assertEquals('member', $roleInfo['organisation_roles'][0]['name']);
    }

    // Optimization Tests (merged from PermissionServiceOptimizationTest)

    public function test_get_user_role_names_optimization(): void
    {
        // Create roles
        $systemRole = Role::firstOrCreate([
            'name' => 'admin',
            'guard_name' => 'system',
            'organisation_id' => null,
        ]);

        $orgRole = Role::firstOrCreate([
            'name' => 'owner',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        // Assign roles
        $this->permissionService->assignRoleToUser($this->user, $systemRole);
        $this->permissionService->assignRoleToUser($this->user, $orgRole);

        // Test optimized method
        $roleNames = $this->permissionService->getUserRoleNames($this->user);

        $this->assertCount(2, $roleNames);
        $this->assertTrue($roleNames->contains('admin'));
        $this->assertTrue($roleNames->contains('owner'));
    }

    public function test_get_assignable_roles_optimization(): void
    {
        // Create test users
        $rootUser = User::factory()->create();
        $adminUser = User::factory()->create();
        $ownerUser = User::factory()->create();

        // Attach users to organisation
        $rootUser->organisations()->attach($this->organisation->id);
        $adminUser->organisations()->attach($this->organisation->id);
        $ownerUser->organisations()->attach($this->organisation->id);

        // Create all role types
        $rootRole = Role::firstOrCreate([
            'name' => 'root',
            'guard_name' => 'system',
            'organisation_id' => null,
        ]);

        $adminRole = Role::firstOrCreate([
            'name' => 'admin',
            'guard_name' => 'system',
            'organisation_id' => null,
        ]);

        $ownerRole = Role::firstOrCreate([
            'name' => 'owner',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        $memberRole = Role::firstOrCreate([
            'name' => 'member',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        // Assign roles
        $this->permissionService->assignRoleToUser($rootUser, $rootRole);
        $this->permissionService->assignRoleToUser($adminUser, $adminRole);
        $this->permissionService->assignRoleToUser($ownerUser, $ownerRole);

        // Test root user can assign all lower-level roles
        $rootAssignableRoles = $this->permissionService->getAssignableRoles($rootUser);

        $this->assertContains('admin', $rootAssignableRoles);
        $this->assertContains('owner', $rootAssignableRoles);
        $this->assertContains('member', $rootAssignableRoles);
        $this->assertNotContains('root', $rootAssignableRoles); // Cannot assign same level

        // Test admin user can assign organisation roles only
        $adminAssignableRoles = $this->permissionService->getAssignableRoles($adminUser);

        $this->assertContains('owner', $adminAssignableRoles);
        $this->assertContains('member', $adminAssignableRoles);
        $this->assertNotContains('admin', $adminAssignableRoles); // Cannot assign same level
        $this->assertNotContains('root', $adminAssignableRoles); // Cannot assign higher level

        // Test owner user can assign member role only
        $ownerAssignableRoles = $this->permissionService->getAssignableRoles($ownerUser);

        $this->assertContains('member', $ownerAssignableRoles);
        $this->assertNotContains('owner', $ownerAssignableRoles); // Cannot assign same level
        $this->assertNotContains('admin', $ownerAssignableRoles); // Cannot assign higher level
        $this->assertNotContains('root', $ownerAssignableRoles); // Cannot assign higher level
    }

    public function test_role_hierarchy_consistency(): void
    {
        // Create test user
        $user = User::factory()->create();
        $user->organisations()->attach($this->organisation->id);

        // Create all role types that should exist
        $rootRole = Role::firstOrCreate([
            'name' => 'root',
            'guard_name' => 'system',
            'organisation_id' => null,
        ]);

        $adminRole = Role::firstOrCreate([
            'name' => 'admin',
            'guard_name' => 'system',
            'organisation_id' => null,
        ]);

        $ownerRole = Role::firstOrCreate([
            'name' => 'owner',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        $memberRole = Role::firstOrCreate([
            'name' => 'member',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        $this->permissionService->assignRoleToUser($user, $rootRole);

        // Get assignable roles
        $assignableRoles = $this->permissionService->getAssignableRoles($user);

        // Root (level 4) should be able to assign admin (3), owner (2), member (1)
        // but not root (4) - same level
        $expectedAssignableRoles = ['admin', 'owner', 'member'];

        foreach ($expectedAssignableRoles as $expectedRole) {
            $this->assertContains(
                $expectedRole,
                $assignableRoles,
                "Root user should be able to assign {$expectedRole} role"
            );
        }

        $this->assertNotContains(
            'root',
            $assignableRoles,
            "Root user should not be able to assign root role (same level)"
        );
    }
}
