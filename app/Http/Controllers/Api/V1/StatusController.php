<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Status Controller
 * 
 * Handles API status and health check endpoints
 */
final class StatusController extends ApiController
{
    /**
     * Get API status information
     */
    public function index(): JsonResponse
    {
        $status = [
            'service' => config('app.name'),
            'version' => '1.0.0',
            'environment' => config('app.env'),
            'debug' => config('app.debug'),
            'timezone' => config('app.timezone'),
            'locale' => config('app.locale'),
            'uptime' => $this->getUptime(),
            'memory_usage' => $this->getMemoryUsage(),
        ];

        return $this->successResponse($status, 'API is running successfully');
    }

    /**
     * Health check endpoint
     */
    public function health(): JsonResponse
    {
        // Perform basic health checks
        $checks = [
            'database' => $this->checkDatabase(),
            'cache' => $this->checkCache(),
            'storage' => $this->checkStorage(),
        ];

        $allHealthy = collect($checks)->every(fn($check) => $check['status'] === 'ok');
        $statusCode = $allHealthy ? 200 : 503;

        return response()->json([
            'status' => $allHealthy ? 'healthy' : 'unhealthy',
            'checks' => $checks,
            'timestamp' => now()->toISOString(),
        ], $statusCode);
    }

    /**
     * Get system uptime
     */
    private function getUptime(): array
    {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
        } else {
            $load = [0, 0, 0];
        }

        return [
            'load_average' => $load,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Get memory usage information
     */
    private function getMemoryUsage(): array
    {
        return [
            'current' => memory_get_usage(true),
            'peak' => memory_get_peak_usage(true),
            'limit' => ini_get('memory_limit'),
        ];
    }

    /**
     * Check database connectivity
     */
    private function checkDatabase(): array
    {
        try {
            \DB::connection()->getPdo();
            return ['status' => 'ok', 'message' => 'Database connection successful'];
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => 'Database connection failed: ' . $e->getMessage()];
        }
    }

    /**
     * Check cache connectivity
     */
    private function checkCache(): array
    {
        try {
            \Cache::put('health_check', 'ok', 60);
            $value = \Cache::get('health_check');
            return ['status' => $value === 'ok' ? 'ok' : 'error', 'message' => 'Cache check completed'];
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => 'Cache check failed: ' . $e->getMessage()];
        }
    }

    /**
     * Check storage accessibility
     */
    private function checkStorage(): array
    {
        try {
            $testFile = 'health_check_' . time() . '.txt';
            \Storage::put($testFile, 'health check');
            $exists = \Storage::exists($testFile);
            \Storage::delete($testFile);
            
            return ['status' => $exists ? 'ok' : 'error', 'message' => 'Storage check completed'];
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => 'Storage check failed: ' . $e->getMessage()];
        }
    }
}
