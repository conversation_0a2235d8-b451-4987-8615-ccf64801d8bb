<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Base API Resource
 * 
 * Provides standardized response format for API resources
 */
abstract class ApiResource extends JsonResource
{
    /**
     * Transform the resource into an array
     */
    public function toArray(Request $request): array
    {
        return parent::toArray($request);
    }

    /**
     * Get additional data that should be returned with the resource array
     */
    public function with(Request $request): array
    {
        return [
            'success' => true,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Customize the response for a request
     */
    public function withResponse(Request $request, $response): void
    {
        $response->header('Content-Type', 'application/json');
        $response->header('X-API-Version', '1.0');
    }
}
