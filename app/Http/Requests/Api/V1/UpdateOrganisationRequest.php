<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

final class UpdateOrganisationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $organisationId = $this->route('organisation');

        return [
            'name' => ['sometimes', 'required', 'string', 'max:255'],
            'code' => [
                'sometimes',
                'required',
                'string',
                'max:50',
                Rule::unique('organisations', 'code')->ignore($organisationId)
            ],
            'details' => ['sometimes', 'nullable', 'array'],
            'remarks' => ['sometimes', 'nullable', 'string', 'max:1000'],
            'status' => ['sometimes', 'string', 'in:pending,active,suspended'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => '组织名称是必填项',
            'name.string' => '组织名称必须是字符串',
            'name.max' => '组织名称不能超过255个字符',
            'code.required' => '组织代码是必填项',
            'code.string' => '组织代码必须是字符串',
            'code.max' => '组织代码不能超过50个字符',
            'code.unique' => '组织代码已存在',
            'details.array' => '详细信息必须是数组格式',
            'remarks.string' => '备注必须是字符串',
            'remarks.max' => '备注不能超过1000个字符',
            'status.string' => '状态必须是字符串',
            'status.in' => '状态必须是pending、active或suspended之一',
        ];
    }
}
