<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;

final class CreateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization will be handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email|max:255',
            'password' => 'required|string|min:8|max:255',
            'organisation_ids' => 'nullable|array',
            'organisation_ids.*' => 'integer|exists:organisations,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => '用户名称为必填项',
            'name.string' => '用户名称必须是字符串',
            'name.max' => '用户名称不能超过255个字符',
            'email.required' => '邮箱地址为必填项',
            'email.email' => '邮箱地址格式不正确',
            'email.unique' => '该邮箱地址已被使用',
            'email.max' => '邮箱地址不能超过255个字符',
            'password.required' => '密码为必填项',
            'password.string' => '密码必须是字符串',
            'password.min' => '密码至少需要8个字符',
            'password.max' => '密码不能超过255个字符',
            'organisation_ids.array' => '组织ID列表必须是数组',
            'organisation_ids.*.integer' => '组织ID必须是整数',
            'organisation_ids.*.exists' => '指定的组织不存在',
        ];
    }
}
