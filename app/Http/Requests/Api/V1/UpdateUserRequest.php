<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

final class UpdateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization will be handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $userId = $this->route('user');

        return [
            'name' => 'sometimes|string|max:255',
            'email' => [
                'sometimes',
                'email',
                'max:255',
                Rule::unique('users', 'email')->ignore($userId),
            ],
            'password' => 'sometimes|string|min:8|max:255',
            'organisation_ids' => 'sometimes|nullable|array',
            'organisation_ids.*' => 'integer|exists:organisations,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.string' => '用户名称必须是字符串',
            'name.max' => '用户名称不能超过255个字符',
            'email.email' => '邮箱地址格式不正确',
            'email.unique' => '该邮箱地址已被使用',
            'email.max' => '邮箱地址不能超过255个字符',
            'password.string' => '密码必须是字符串',
            'password.min' => '密码至少需要8个字符',
            'password.max' => '密码不能超过255个字符',
            'organisation_ids.array' => '组织ID列表必须是数组',
            'organisation_ids.*.integer' => '组织ID必须是整数',
            'organisation_ids.*.exists' => '指定的组织不存在',
        ];
    }
}
