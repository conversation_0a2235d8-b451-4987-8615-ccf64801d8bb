<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * API Middleware
 * 
 * Handles API-specific concerns like CORS, content type, and response headers
 */
final class ApiMiddleware
{
    /**
     * Handle an incoming request
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Ensure JSON content type for API requests
        if (!$request->isJson() && $request->isMethod('POST', 'PUT', 'PATCH')) {
            $request->headers->set('Content-Type', 'application/json');
        }

        // Process the request
        $response = $next($request);

        // Add API-specific headers
        $response->headers->set('Content-Type', 'application/json');
        $response->headers->set('X-API-Version', '1.0');
        $response->headers->set('X-Powered-By', config('app.name'));

        // Handle CORS
        $response->headers->set('Access-Control-Allow-Origin', '*');
        $response->headers->set('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS');
        $response->headers->set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin');
        $response->headers->set('Access-Control-Max-Age', '86400');

        // Handle preflight OPTIONS requests
        if ($request->isMethod('OPTIONS')) {
            $response->setStatusCode(200);
            $response->setContent('');
        }

        return $response;
    }
}
