<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\Organisation;
use App\Models\User;
use Illuminate\Auth\Access\Response;

final class OrganisationPolicy
{
    /**
     * Determine whether the user can view any models.
     * Based on OrganisationController middleware (AdminAccessMiddleware).
     */
    public function viewAny(User $user): bool
    {
        // Only system admins can view all organisations
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can view the model.
     * System admins can view any organisation, organisation owners can view their own organisations.
     */
    public function view(User $user, Organisation $organisation): bool
    {
        // Organisation owners can view their own organisations
        return $user->hasOrganisationAdminAccess($organisation->id);
    }

    /**
     * Determine whether the user can create models.
     * Based on OrganisationController middleware (AdminAccessMiddleware).
     */
    public function create(User $user): bool
    {
        // Only system admins can create organisations
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can update the model.
     * System admins can update any organisation, organisation owners can update their own organisations.
     */
    public function update(User $user, Organisation $organisation): bool
    {
        // Organisation owners can update their own organisations
        return $user->hasOrganisationAdminAccess($organisation->id);
    }

    /**
     * Determine whether the user can suspend the model.
     * Based on OrganisationController::suspend() - only system admins can suspend organisations.
     */
    public function suspend(User $user, Organisation $organisation): bool
    {
        // Only system admins can suspend organisations
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can activate the model.
     * Only system admins can activate organisations.
     */
    public function activate(User $user, Organisation $organisation): bool
    {
        // Only system admins can activate organisations
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Organisation $organisation): bool
    {
        // Only system admins can delete organisations
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Organisation $organisation): bool
    {
        // Only system admins can restore organisations
        return $user->hasSystemAdminAccess();
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Organisation $organisation): bool
    {
        // Only system admins can permanently delete organisations
        return $user->hasSystemAdminAccess();
    }
}
