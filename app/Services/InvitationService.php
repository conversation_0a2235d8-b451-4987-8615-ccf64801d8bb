<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Invitation;
use App\Models\Role;
use App\Models\User;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

final class InvitationService
{
    public function __construct(
        private readonly PermissionService $permissionService
    ) {}

    /**
     * Create a new invitation with business logic validation
     */
    public function createInvitation(array $data, User $user): Invitation
    {
        // Additional business validation for organization-specific permissions
        if (!$user->hasSystemAdminAccess() && !$user->hasOrganisationAdminAccess((int)$data['model_id'])) {
            throw ValidationException::withMessages([
                'authorization' => ['You do not have permission to create invitations for this organization']
            ]);
        }

        // Security check: prevent system role invitations
        if (in_array($data['role'], ['root', 'admin'])) {
            throw ValidationException::withMessages([
                'role' => ['Cannot create invitations for system roles']
            ]);
        }

        return Invitation::create([
            'model_type' => $data['model_type'],
            'model_id' => $data['model_id'],
            'role' => $data['role'],
            'created_by_user_id' => $user->id,
            'expires_at' => $data['expires_at'] ?? Carbon::now()->addWeek(),
            'max_uses' => $data['max_uses'] ?? 1,
            'email_restriction' => $data['email_restriction'] ?? null,
        ]);
    }

    /**
     * Accept invitation and assign role to user
     */
    public function acceptInvitation(Invitation $invitation, User $user): array
    {
        // Check email restriction if set
        if (!$invitation->isEmailAllowed($user->email)) {
            throw ValidationException::withMessages([
                'email' => ['Your email address is not allowed to accept this invitation']
            ]);
        }

        try {
            DB::beginTransaction();

            // Find the target model (should be Organization)
            $targetModel = $invitation->model;
            if (!$targetModel) {
                throw ValidationException::withMessages([
                    'invitation' => ['Invitation associated resource does not exist']
                ]);
            }

            // For Organization model, we need to find the role within that organization
            if ($invitation->model_type === 'App\Models\Organisation') {
                $result = $this->processOrganizationInvitation($invitation, $user, $targetModel);
                
                DB::commit();
                
                return $result;
            }

            throw ValidationException::withMessages([
                'invitation' => ['Unsupported invitation type']
            ]);

        } catch (ValidationException $e) {
            DB::rollBack();
            throw $e;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Exception('Error processing invitation: ' . $e->getMessage());
        }
    }

    /**
     * Process organization invitation acceptance
     */
    private function processOrganizationInvitation(Invitation $invitation, User $user, $targetModel): array
    {
        $role = Role::where('name', $invitation->role)
            ->where('guard_name', 'api')
            ->where('organisation_id', $invitation->model_id)
            ->first();

        if (!$role) {
            throw ValidationException::withMessages([
                'role' => ['Specified role does not exist']
            ]);
        }

        // Check if user is already a member of this organization
        if (!$user->organisations()->where('organisation_id', $invitation->model_id)->exists()) {
            // Add user to organization
            $user->organisations()->attach($invitation->model_id);
        }

        // Assign role to user
        $this->permissionService->assignRoleToUser($user, $role);

        // Verify email if not already verified
        if (!$user->email_verified_at) {
            $user->email_verified_at = now();
            $user->save();
        }

        // Increment invitation usage
        $invitation->incrementUsage();

        // Get updated role information
        $roleInfo = $this->permissionService->getUserCompleteRoleInfo($user);

        return [
            'message' => 'Successfully joined organization',
            'organization' => [
                'id' => $targetModel->id,
                'name' => $targetModel->name,
            ],
            'role' => $invitation->role,
            'user_roles' => $roleInfo,
        ];
    }

    /**
     * Check if invitation is valid and can be accepted
     */
    public function validateInvitationForAcceptance(Invitation $invitation): void
    {
        if (!$invitation->isValid()) {
            if ($invitation->isExpired()) {
                throw ValidationException::withMessages([
                    'invitation' => ['Invitation link has expired']
                ]);
            }

            if ($invitation->hasReachedUsageLimit()) {
                throw ValidationException::withMessages([
                    'invitation' => ['Invitation link has reached usage limit']
                ]);
            }
        }
    }

    /**
     * Get invitations with filtering and pagination
     */
    public function getInvitations(
        User $user,
        int $perPage = 15,
        ?string $modelType = null,
        ?int $modelId = null,
        ?string $role = null,
        ?string $status = null
    ): LengthAwarePaginator {
        // Build query based on user permissions
        $query = Invitation::with(['model', 'createdBy']);

        if ($user->hasSystemAdminAccess()) {
            // System admins can view all invitations - no additional filtering needed
        } else {
            // Organization owners can only view invitations they created
            $query->where('created_by_user_id', $user->id);
        }

        // Apply optional filters
        if ($modelType) {
            $query->where('model_type', $modelType);
        }

        if ($modelId) {
            $query->where('model_id', $modelId);
        }

        if ($role) {
            $query->where('role', $role);
        }

        if ($status) {
            if ($status === 'valid') {
                $query->where('expires_at', '>', now())
                      ->whereColumn('uses', '<', 'max_uses');
            } elseif ($status === 'expired') {
                $query->where('expires_at', '<=', now());
            } elseif ($status === 'used_up') {
                $query->whereColumn('uses', '>=', 'max_uses');
            }
        }

        // Order by creation date (newest first)
        $query->orderBy('created_at', 'desc');

        // Paginate results
        return $query->paginate($perPage);
    }

    /**
     * Validate invitation for viewing (business logic validation)
     */
    public function validateInvitationForViewing(Invitation $invitation): void
    {
        if (!$invitation->isValid()) {
            if ($invitation->isExpired()) {
                throw ValidationException::withMessages([
                    'invitation' => ['Invitation link has expired']
                ]);
            }

            if ($invitation->hasReachedUsageLimit()) {
                throw ValidationException::withMessages([
                    'invitation' => ['Invitation link has reached usage limit']
                ]);
            }
        }
    }
}
