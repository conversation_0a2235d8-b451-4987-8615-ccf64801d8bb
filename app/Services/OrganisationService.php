<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Organisation;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

final readonly class OrganisationService
{
    /**
     * Get all organisations with optional pagination.
     */
    public function getAll(int $perPage = 15): LengthAwarePaginator
    {
        return Organisation::orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Get organisation by ID.
     */
    public function getById(int $id): ?Organisation
    {
        return Organisation::find($id);
    }

    /**
     * Get organisation by code.
     */
    public function getByCode(string $code): ?Organisation
    {
        return Organisation::where('code', $code)->first();
    }

    /**
     * Create a new organisation.
     */
    public function create(array $data): Organisation
    {
        return Organisation::create($data);
    }

    /**
     * Update an organisation.
     */
    public function update(Organisation $organisation, array $data): bool
    {
        return $organisation->update($data);
    }



    /**
     * Suspend an organisation.
     */
    public function suspend(Organisation $organisation): bool
    {
        return $organisation->suspend();
    }

    /**
     * Get organisations by status.
     */
    public function getByStatus(string $status, int $perPage = 15): LengthAwarePaginator
    {
        return Organisation::where('status', $status)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Check if organisation code is unique.
     */
    public function isCodeUnique(string $code, ?int $excludeId = null): bool
    {
        $query = Organisation::where('code', $code);
        
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        
        return !$query->exists();
    }

    /**
     * Get active organisations.
     */
    public function getActive(int $perPage = 15): LengthAwarePaginator
    {
        return $this->getByStatus('active', $perPage);
    }

    /**
     * Get pending organisations.
     */
    public function getPending(int $perPage = 15): LengthAwarePaginator
    {
        return $this->getByStatus('pending', $perPage);
    }

    /**
     * Get suspended organisations.
     */
    public function getSuspended(int $perPage = 15): LengthAwarePaginator
    {
        return $this->getByStatus('suspended', $perPage);
    }
}
