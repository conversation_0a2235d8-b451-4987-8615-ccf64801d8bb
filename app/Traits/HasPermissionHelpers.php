<?php

declare(strict_types=1);

namespace App\Traits;

use App\Models\Role;
use App\Models\User;
use Illuminate\Support\Collection;

trait HasPermissionHelpers
{
    /**
     * Role hierarchy levels for permission checking.
     */
    public const ROLE_HIERARCHY = [
        'root' => 4,
        'admin' => 3,
        'owner' => 2,
        'member' => 1,
    ];
    /**
     * Check if both users belong to at least one common organisation.
     */
    public function hasCommonOrganisation(User $user1, User $user2): bool
    {
        $user1OrganisationIds = $user1->getOrganisationIds();
        $user2OrganisationIds = $user2->getOrganisationIds();

        if ($user1OrganisationIds->isEmpty()) {
            return false;
        }

        // Check if there's any common organisation
        $commonOrganisations = $user1OrganisationIds->intersect($user2OrganisationIds);
        return !$commonOrganisations->isEmpty();
    }

    /**
     * Check if user has access to all specified organisations.
     */
    public function hasAccessToAllOrganisations(User $user, array $organisationIds): bool
    {
        if (empty($organisationIds)) {
            return true;
        }

        $userOrganisationIds = $user->getOrganisationIds();
        $invalidOrganisationIds = array_diff($organisationIds, $userOrganisationIds->toArray());
        
        return empty($invalidOrganisationIds);
    }

    /**
     * Check if user has access to a specific organisation.
     */
    public function hasAccessToOrganisation(User $user, int $organisationId): bool
    {
        $userOrganisationIds = $user->getOrganisationIds();
        return $userOrganisationIds->contains($organisationId);
    }

    /**
     * Get organisations that user can access for filtering purposes.
     */
    public function getAccessibleOrganisationIds(User $user): Collection
    {
        return $user->getOrganisationIds();
    }

    /**
     * Check if user can access another user based on organisation membership.
     */
    public function canAccessUserByOrganisation(User $currentUser, User $targetUser): bool
    {
        // System admins can access any user
        if ($currentUser->hasSystemAdminAccess()) {
            return true;
        }

        // Check if both users belong to at least one common organisation
        return $this->hasCommonOrganisation($currentUser, $targetUser);
    }

    /**
     * Get user's highest role level.
     */
    public function getUserHighestRoleLevel(User $user): int
    {
        $userRoles = $user->getAllRoleNames();
        $maxLevel = 0;

        foreach ($userRoles as $roleName) {
            $level = self::ROLE_HIERARCHY[$roleName] ?? 0;
            $maxLevel = max($maxLevel, $level);
        }

        return $maxLevel;
    }

    /**
     * Check if user can assign specific role based on assignment rules.
     */
    public function canAssignRole(User $assigner, User $targetUser, Role $role): bool
    {
        $assignerRoles = $assigner->getAllRoleNames();
        $roleName = $role->name;

        // Root can assign any role
        if ($assignerRoles->contains('root')) {
            return $this->canRootAssign($targetUser, $role);
        }

        // Admin can assign organisation-related roles
        if ($assignerRoles->contains('admin')) {
            return $this->canAdminAssign($targetUser, $role);
        }

        // Owner can assign member role within their organisation
        if ($assignerRoles->contains('owner')) {
            return $this->canOwnerAssign($assigner, $targetUser, $role);
        }

        return false;
    }

    /**
     * Check if root can assign role to target user.
     */
    public function canRootAssign(User $targetUser, Role $role): bool
    {
        // Root can assign any role to any user
        return true;
    }

    /**
     * Check if admin can assign role to target user.
     */
    public function canAdminAssign(User $targetUser, Role $role): bool
    {
        // Admin can assign organisation-related roles (owner, member)
        return in_array($role->name, ['owner', 'member']);
    }

    /**
     * Check if owner can assign role to target user.
     */
    public function canOwnerAssign(User $assigner, User $targetUser, Role $role): bool
    {
        // Owner can only assign member role
        if ($role->name !== 'member') {
            return false;
        }

        // Owner can only assign within their own organisations
        if (!$role->organisation_id) {
            return false;
        }

        // Check if assigner has owner role for this organisation
        return $assigner->hasOwnerRoleForOrganisation($role->organisation_id);
    }
}
