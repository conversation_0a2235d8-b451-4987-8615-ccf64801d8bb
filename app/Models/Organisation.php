<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

final class Organisation extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'code',
        'details',
        'remarks',
        'status',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'details' => 'array',
        ];
    }

    /**
     * Get the users for the organisation.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_organisation')
            ->withTimestamps();
    }

    /**
     * Check if the organisation is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if the organisation is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the organisation is suspended.
     */
    public function isSuspended(): bool
    {
        return $this->status === 'suspended';
    }



    /**
     * Suspend the organisation.
     */
    public function suspend(): bool
    {
        return $this->update(['status' => 'suspended']);
    }
}
